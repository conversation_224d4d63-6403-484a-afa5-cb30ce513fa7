
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Layout } from "./components/Layout";
import { SettingsLayout } from "./components/SettingsLayout";
import { Dashboard } from "./pages/Dashboard";
import { SettingsDefaults } from "./pages/SettingsDefaults";
import { SettingsSystem } from "./pages/SettingsSystem";
import { SettingsPersonalization } from "./pages/SettingsPersonalization";
import { SettingsProfile } from "./pages/SettingsProfile";
import { SettingsAccount } from "./pages/SettingsAccount";
import { SettingsPlansAndBilling } from "./pages/SettingsPlansAndBilling";
import { SettingsDataAndPrivacy } from "./pages/SettingsDataAndPrivacy";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/settings" element={<SettingsLayout />}>
              <Route index element={<SettingsDefaults />} />
              <Route path="system" element={<SettingsSystem />} />
              <Route path="personalization" element={<SettingsPersonalization />} />
              <Route path="profile" element={<SettingsProfile />} />
              <Route path="account" element={<SettingsAccount />} />
              <Route path="plans-billing" element={<SettingsPlansAndBilling />} />
              <Route path="data-privacy" element={<SettingsDataAndPrivacy />} />
            </Route>
            <Route path="*" element={<NotFound />} />
          </Routes>
        </Layout>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
