
import React from 'react';
import { NavLink } from 'react-router-dom';
import { Home, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

export const Sidebar: React.FC = () => {
  return (
    <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
      {/* Logo */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center space-x-2">
          <h1 className="text-xl font-semibold text-gray-900">Flow</h1>
          <span className="bg-purple-600 text-white text-xs px-2 py-1 rounded-full font-medium">
            Pro
          </span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        <NavLink
          to="/"
          className={({ isActive }) =>
            `flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
              isActive
                ? 'bg-primary/10 text-primary font-medium'
                : 'text-gray-600 hover:bg-gray-100'
            }`
          }
        >
          <Home size={18} />
          <span>Home</span>
        </NavLink>

        <NavLink
          to="/settings"
          className={({ isActive }) =>
            `flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
              isActive
                ? 'bg-primary/10 text-primary font-medium'
                : 'text-gray-600 hover:bg-gray-100'
            }`
          }
        >
          <Settings size={18} />
          <span>Settings</span>
        </NavLink>
      </nav>

      {/* Trial Status */}
      <div className="p-4 space-y-4">
        <Card className="p-4 bg-gradient-to-br from-purple-50 to-blue-50 border-purple-200">
          <div className="space-y-3">
            <div>
              <p className="font-medium text-gray-900">Flow Pro Trial</p>
              <p className="text-sm text-gray-600">12 of 14 days used</p>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-purple-600 h-2 rounded-full"
                style={{ width: '86%' }}
              ></div>
            </div>
            <Button className="w-full bg-purple-600 hover:bg-purple-700">
              Upgrade to Flow Pro
            </Button>
          </div>
        </Card>

        <div className="space-y-2">
          <button className="w-full text-left text-sm text-gray-600 hover:text-gray-900 py-1">
            Refer a friend
          </button>
          <button className="w-full text-left text-sm text-gray-600 hover:text-gray-900 py-1">
            Help
          </button>
        </div>

        <div className="pt-2 border-t border-gray-200">
          <p className="text-xs text-gray-500">Flow v1.3.61</p>
        </div>
      </div>
    </div>
  );
};
