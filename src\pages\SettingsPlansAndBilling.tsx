
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Toggle } from '@/components/ui/toggle';

export const SettingsPlansAndBilling: React.FC = () => {
  const [billingCycle, setBillingCycle] = useState('annual');

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Plans and Billing</h1>
      
      {/* Billing Toggle */}
      <div className="flex items-center justify-center space-x-4">
        <span className={billingCycle === 'monthly' ? 'font-medium' : 'text-gray-600'}>Monthly</span>
        <Toggle
          pressed={billingCycle === 'annual'}
          onPressedChange={(pressed) => setBillingCycle(pressed ? 'annual' : 'monthly')}
          className="data-[state=on]:bg-primary"
        />
        <span className={billingCycle === 'annual' ? 'font-medium' : 'text-gray-600'}>Annual</span>
      </div>

      {/* Pricing Tiers */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Basic Plan */}
        <Card className="p-6 border-2">
          <div className="space-y-4">
            <div>
              <h3 className="text-xl font-semibold">Basic</h3>
              <p className="text-2xl font-bold text-green-600">Free</p>
            </div>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• 2,000 words/week</li>
              <li>• Lightning fast voice-typing</li>
              <li>• Dictionary support</li>
              <li>• 100+ languages</li>
            </ul>
            <Button variant="outline" className="w-full" disabled>
              Current Plan
            </Button>
          </div>
        </Card>

        {/* Pro Plan */}
        <Card className="p-6 border-2 border-primary relative">
          <div className="absolute -top-3 left-4 bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
            -20%
          </div>
          <div className="space-y-4">
            <div>
              <h3 className="text-xl font-semibold">Pro</h3>
              <p className="text-2xl font-bold">$144<span className="text-sm text-gray-600">/user/year</span></p>
            </div>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Everything in Basic</li>
              <li>• Unlimited words</li>
              <li>• Command Mode</li>
              <li>• Prioritized requests</li>
              <li>• Early access</li>
              <li>• Unlimited mobile</li>
            </ul>
            <Button className="w-full">
              Upgrade to Pro
            </Button>
          </div>
        </Card>

        {/* Teams Plan */}
        <Card className="p-6 border-2 relative">
          <div className="absolute -top-3 left-4 bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium">
            -33%
          </div>
          <div className="space-y-4">
            <div>
              <h3 className="text-xl font-semibold">Teams</h3>
              <p className="text-2xl font-bold">$120<span className="text-sm text-gray-600">/user/year</span></p>
            </div>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Everything in Pro</li>
              <li>• Centralized billing</li>
              <li>• Admin controls</li>
              <li>• Shared context</li>
              <li>• SSO/SAML options</li>
            </ul>
            <Button variant="outline" className="w-full">
              Create a team
            </Button>
          </div>
        </Card>
      </div>

      {/* Trial Status */}
      <Card className="p-6">
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <p className="font-medium text-gray-900">Flow Pro Trial</p>
            <p className="text-sm text-gray-600">12 of 14 days used</p>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-purple-600 h-2 rounded-full"
              style={{ width: '86%' }}
            ></div>
          </div>
        </div>
      </Card>

      {/* Enterprise */}
      <Card className="p-6 bg-gray-50">
        <div className="text-center space-y-2">
          <p className="text-gray-900">Want Flow for your company?</p>
          <Button variant="link" className="p-0 h-auto text-primary">
            Contact sales
          </Button>
        </div>
      </Card>
    </div>
  );
};
