
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

export const SettingsDefaults: React.FC = () => {
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Defaults</h1>
      
      <div className="space-y-4">
        <Card className="p-6">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="font-medium text-gray-900">Set default keyboard shortcut</h3>
              <p className="text-sm text-gray-600 mt-1">
                Configure the keys you hold to start voice dictation
              </p>
            </div>
            <Button variant="outline">Change shortcut</Button>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="font-medium text-gray-900">Set default microphone</h3>
              <p className="text-sm text-gray-600 mt-1">
                Choose which microphone to use for voice input
              </p>
            </div>
            <Button variant="outline">Select microphone</Button>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="font-medium text-gray-900">Set default language(s)</h3>
              <p className="text-sm text-gray-600 mt-1">
                Configure the languages you want to dictate in
              </p>
            </div>
            <Button variant="outline">Set languages</Button>
          </div>
        </Card>
      </div>
    </div>
  );
};
