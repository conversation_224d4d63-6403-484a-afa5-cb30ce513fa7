
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';

export const SettingsDataAndPrivacy: React.FC = () => {
  const [settings, setSettings] = useState({
    privacyMode: true,
    contextAwareness: true,
  });

  const updateSetting = (key: keyof typeof settings) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Data and Privacy</h1>
      
      <div className="space-y-6">
        {/* Privacy Controls Section */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900">Privacy controls</h2>
          
          <Card className="p-6">
            <div className="flex justify-between items-start">
              <div className="flex-1 pr-4">
                <h3 className="font-medium text-gray-900">Privacy Mode</h3>
                <p className="text-sm text-gray-600 mt-1">
                  If enabled, none of your dictation data will be stored or used for model training by us or any third party (zero data retention)
                </p>
              </div>
              <Switch
                checked={settings.privacyMode}
                onCheckedChange={() => updateSetting('privacyMode')}
              />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex justify-between items-start">
              <div className="flex-1 pr-4">
                <h3 className="font-medium text-gray-900">Context awareness</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Allow Flow to use limited, relevant text content from the app you're dictating in to spell names correctly and better understand you
                </p>
              </div>
              <Switch
                checked={settings.contextAwareness}
                onCheckedChange={() => updateSetting('contextAwareness')}
              />
            </div>
          </Card>
        </div>

        {/* Data Management Section */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900">Data management</h2>
          
          <Card className="p-6">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">Delete history of all activity</h3>
                <p className="text-sm text-gray-600 mt-1">
                  This will delete all of your transcripts and associated data from your device
                </p>
              </div>
              <Button variant="destructive">
                Delete
              </Button>
            </div>
          </Card>
        </div>

        {/* Footer */}
        <div className="pt-4 border-t border-gray-200">
          <Button variant="link" className="p-0 h-auto text-primary">
            Read about our Data Controls
          </Button>
        </div>
      </div>
    </div>
  );
};
