
import React from 'react';
import { NavLink, Outlet } from 'react-router-dom';

export const SettingsLayout: React.FC = () => {
  return (
    <div className="p-8 max-w-6xl mx-auto">
      <div className="flex space-x-8">
        {/* Settings Sidebar */}
        <div className="w-64 space-y-6">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">General</h2>
            <nav className="space-y-1">
              <NavLink
                to="/settings"
                end
                className={({ isActive }) =>
                  `block px-3 py-2 rounded-lg text-sm transition-colors ${
                    isActive
                      ? 'bg-primary/10 text-primary font-medium'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`
                }
              >
                Defaults
              </NavLink>
              <NavLink
                to="/settings/system"
                className={({ isActive }) =>
                  `block px-3 py-2 rounded-lg text-sm transition-colors ${
                    isActive
                      ? 'bg-primary/10 text-primary font-medium'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`
                }
              >
                System
              </NavLink>
              <NavLink
                to="/settings/personalization"
                className={({ isActive }) =>
                  `block px-3 py-2 rounded-lg text-sm transition-colors ${
                    isActive
                      ? 'bg-primary/10 text-primary font-medium'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`
                }
              >
                Personalization
              </NavLink>
            </nav>
          </div>
          
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Account</h2>
            <nav className="space-y-1">
              <NavLink
                to="/settings/profile"
                className={({ isActive }) =>
                  `block px-3 py-2 rounded-lg text-sm transition-colors ${
                    isActive
                      ? 'bg-primary/10 text-primary font-medium'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`
                }
              >
                Profile
              </NavLink>
              <NavLink
                to="/settings/account"
                className={({ isActive }) =>
                  `block px-3 py-2 rounded-lg text-sm transition-colors ${
                    isActive
                      ? 'bg-primary/10 text-primary font-medium'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`
                }
              >
                Account
              </NavLink>
              <NavLink
                to="/settings/plans-billing"
                className={({ isActive }) =>
                  `block px-3 py-2 rounded-lg text-sm transition-colors ${
                    isActive
                      ? 'bg-primary/10 text-primary font-medium'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`
                }
              >
                Plans and Billing
              </NavLink>
              <NavLink
                to="/settings/data-privacy"
                className={({ isActive }) =>
                  `block px-3 py-2 rounded-lg text-sm transition-colors ${
                    isActive
                      ? 'bg-primary/10 text-primary font-medium'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`
                }
              >
                Data and Privacy
              </NavLink>
            </nav>
          </div>
        </div>

        {/* Settings Content */}
        <div className="flex-1">
          <Outlet />
        </div>
      </div>
    </div>
  );
};
