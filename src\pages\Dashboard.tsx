
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

export const Dashboard: React.FC = () => {
  const recentActivity = [
    {
      id: 1,
      time: '3:42 PM',
      content: 'Tôi muốn đặt một cuộc họp vào thứ Ba tới',
      type: 'Vietnamese'
    },
    {
      id: 2,
      time: '3:41 PM',
      content: 'Schedule meeting for next Tuesday at 2 PM',
      type: 'English'
    },
    {
      id: 3,
      time: '3:39 PM',
      content: 'Add markers to the PDF coordination system for better tracking',
      type: 'Technical note'
    },
    {
      id: 4,
      time: '3:35 PM',
      content: 'Cần phải hoàn thành báo cáo trước 5 giờ chiều',
      type: 'Vietnamese'
    }
  ];

  return (
    <div className="p-8 space-y-8 max-w-6xl mx-auto">
      {/* Welcome Section */}
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome back, Sam</h1>
          <div className="flex items-center space-x-8 text-sm text-gray-600">
            <div className="flex items-center space-x-2">
              <span className="font-medium text-gray-900">2 weeks</span>
              <span>active</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="font-medium text-gray-900">440 words</span>
              <span>dictated</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="font-medium text-gray-900">55 WPM</span>
              <span>average speed</span>
            </div>
          </div>
        </div>

        {/* Main Feature Card */}
        <Card className="p-8 bg-gradient-to-br from-primary/5 to-accent border-primary/20">
          <div className="space-y-4">
            <h2 className="text-2xl font-semibold text-gray-900">
              Voice dictation in any app
            </h2>
            <p className="text-gray-600 text-lg">
              Hold down the trigger keys <kbd className="px-2 py-1 bg-gray-100 rounded text-sm font-mono">ctrl + alt</kbd> and speak into any textbox
            </p>
            <Button className="bg-primary hover:bg-primary/90">
              Explore use cases
            </Button>
          </div>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold text-gray-900">Recent activity</h3>
        <div className="space-y-3">
          <p className="text-sm text-gray-500 font-medium">May 26, 2025</p>
          
          {recentActivity.map((item) => (
            <Card key={item.id} className="p-4 hover:shadow-md transition-shadow">
              <div className="flex justify-between items-start">
                <div className="flex-1 space-y-1">
                  <p className="text-gray-900">{item.content}</p>
                  <p className="text-xs text-gray-500">{item.type}</p>
                </div>
                <span className="text-sm text-gray-500 ml-4">{item.time}</span>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};
