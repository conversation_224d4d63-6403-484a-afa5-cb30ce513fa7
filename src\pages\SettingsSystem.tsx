
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';

export const SettingsSystem: React.FC = () => {
  const [settings, setSettings] = useState({
    launchAtLogin: false,
    showFlowBar: false,
    showInDock: false,
    interactionSounds: true,
    muteAudioWhenDictating: true,
  });

  const updateSetting = (key: keyof typeof settings) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">System</h1>
      
      <div className="space-y-6">
        {/* App Behavior Section */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900">App behavior</h2>
          
          <Card className="p-6">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">Launch app at login</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Open Flow automatically when your computer starts
                </p>
              </div>
              <Switch
                checked={settings.launchAtLogin}
                onCheckedChange={() => updateSetting('launchAtLogin')}
              />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">Show Flow bar at all times</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Keep the dictation bar at the bottom of your screen visible at all times
                </p>
              </div>
              <Switch
                checked={settings.showFlowBar}
                onCheckedChange={() => updateSetting('showFlowBar')}
              />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">Show app in dock</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Display Flow in your Mac dock for quick access
                </p>
              </div>
              <Switch
                checked={settings.showInDock}
                onCheckedChange={() => updateSetting('showInDock')}
              />
            </div>
          </Card>
        </div>

        {/* Audio Section */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900">Audio</h2>
          
          <Card className="p-6">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">Interaction sounds</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Play sounds for key actions like start/stop
                </p>
              </div>
              <Switch
                checked={settings.interactionSounds}
                onCheckedChange={() => updateSetting('interactionSounds')}
              />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">Mute audio when dictating</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Automatically silence other active audio during dictation
                </p>
              </div>
              <Switch
                checked={settings.muteAudioWhenDictating}
                onCheckedChange={() => updateSetting('muteAudioWhenDictating')}
              />
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};
